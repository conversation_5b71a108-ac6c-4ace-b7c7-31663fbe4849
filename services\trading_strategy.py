import logging
import random
import pandas as pd
import pandas_ta as ta
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from io import BytesIO

from services.binance_service import ExchangeService
from services.crypto_data_service import CryptoDataService
from models.trade import TradeSignal
from config.settings import TRADING_CONCEPTS, MIN_WIN_RATE_PERCENT

logger = logging.getLogger(__name__)

class TradingStrategy:
    """Service for analyzing trading strategies and generating signals."""

    def __init__(self, exchange=None):
        self.exchange = exchange if exchange else ExchangeService()
        self.crypto_data = CryptoDataService()

    def analyze_ict(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Analyze Institutional Candle Theory (ICT) concepts using advanced market structure analysis.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Tuple of (concepts_met, explanation)
        """
        if len(df) < 50:
            return False, ""

        current_price = df['close'].iloc[-1]
        explanations = []

        # 1. Enhanced Liquidity Sweep Detection
        # Look for sweeps of recent highs/lows followed by reversals
        lookback_period = 20
        recent_highs = df['high'].rolling(5).max().iloc[-lookback_period:-1]
        recent_lows = df['low'].rolling(5).min().iloc[-lookback_period:-1]

        # Check for bullish liquidity sweep (sweep low then reverse up)
        recent_significant_low = recent_lows.min()
        if df['low'].iloc[-3:].min() < recent_significant_low:
            # Price swept below recent low
            if current_price > recent_significant_low * 1.005:  # 0.5% above the swept low
                explanations.append("Bullish liquidity sweep: Price swept below recent lows and reversed higher")

        # Check for bearish liquidity sweep (sweep high then reverse down)
        recent_significant_high = recent_highs.max()
        if df['high'].iloc[-3:].max() > recent_significant_high:
            # Price swept above recent high
            if current_price < recent_significant_high * 0.995:  # 0.5% below the swept high
                explanations.append("Bearish liquidity sweep: Price swept above recent highs and reversed lower")

        # 2. Enhanced Fair Value Gap (FVG) Detection
        # Look for gaps between candle bodies that haven't been filled
        for i in range(len(df) - 3, max(len(df) - 15, 0), -1):
            if i < 2 or i+2 >= len(df):
                continue

            # Bullish FVG: Gap between high of candle i and low of candle i+2
            if df['high'].iloc[i] < df['low'].iloc[i+2]:
                gap_low = df['high'].iloc[i]
                gap_high = df['low'].iloc[i+2]

                # Check if current price is near or in the gap
                if gap_low <= current_price <= gap_high:
                    explanations.append(f"Bullish Fair Value Gap: Price trading in unfilled gap ({gap_low:.6f} - {gap_high:.6f})")
                elif current_price > gap_high and current_price < gap_high * 1.02:
                    explanations.append("Bullish Fair Value Gap: Price above unfilled gap, potential support")

            # Bearish FVG: Gap between low of candle i and high of candle i+2
            elif df['low'].iloc[i] > df['high'].iloc[i+2]:
                gap_high = df['low'].iloc[i]
                gap_low = df['high'].iloc[i+2]

                # Check if current price is near or in the gap
                if gap_low <= current_price <= gap_high:
                    explanations.append(f"Bearish Fair Value Gap: Price trading in unfilled gap ({gap_low:.6f} - {gap_high:.6f})")
                elif current_price < gap_low and current_price > gap_low * 0.98:
                    explanations.append("Bearish Fair Value Gap: Price below unfilled gap, potential resistance")

        # 3. Enhanced Order Block Detection
        # Look for high volume candles followed by significant moves
        df['volume_ma'] = df['volume'].rolling(14).mean()
        df['volume_spike'] = df['volume'] > (df['volume_ma'] * 1.8)  # 80% above average

        # Find recent volume spikes
        volume_spikes = df[df['volume_spike']].iloc[-10:]  # Last 10 volume spikes

        for idx, spike_row in volume_spikes.iterrows():
            spike_pos = df.index.get_loc(idx)

            # Skip if too recent (need at least 2 candles after)
            if spike_pos >= len(df) - 2:
                continue

            spike_high = spike_row['high']
            spike_low = spike_row['low']
            spike_close = spike_row['close']

            # Check for bullish order block (high volume followed by upward move)
            if spike_pos < len(df) - 3:
                future_high = df['high'].iloc[spike_pos+1:spike_pos+4].max()
                if future_high > spike_high * 1.015:  # 1.5% move up
                    # Check if current price is near the order block
                    if spike_low <= current_price <= spike_high * 1.01:
                        explanations.append(f"Bullish Order Block: High volume at {spike_close:.6f} followed by upward move")

            # Check for bearish order block (high volume followed by downward move)
            if spike_pos < len(df) - 3:
                future_low = df['low'].iloc[spike_pos+1:spike_pos+4].min()
                if future_low < spike_low * 0.985:  # 1.5% move down
                    # Check if current price is near the order block
                    if spike_low * 0.99 <= current_price <= spike_high:
                        explanations.append(f"Bearish Order Block: High volume at {spike_close:.6f} followed by downward move")

        # 4. Market Structure Shift Detection
        # Look for break of structure (BOS) or change of character (CHoCH)
        swing_highs = []
        swing_lows = []

        # Find swing points using a 5-period window
        for i in range(2, len(df) - 2):
            # Swing high: higher than 2 periods before and after
            if (df['high'].iloc[i] > df['high'].iloc[i-2:i].max() and
                df['high'].iloc[i] > df['high'].iloc[i+1:i+3].max()):
                swing_highs.append((i, df['high'].iloc[i]))

            # Swing low: lower than 2 periods before and after
            if (df['low'].iloc[i] < df['low'].iloc[i-2:i].min() and
                df['low'].iloc[i] < df['low'].iloc[i+1:i+3].min()):
                swing_lows.append((i, df['low'].iloc[i]))

        # Check for recent break of structure
        if len(swing_highs) >= 2:
            recent_high = swing_highs[-1][1]
            prev_high = swing_highs[-2][1]
            if current_price > recent_high and recent_high > prev_high:
                explanations.append("Bullish Break of Structure: Price broke above recent swing high")

        if len(swing_lows) >= 2:
            recent_low = swing_lows[-1][1]
            prev_low = swing_lows[-2][1]
            if current_price < recent_low and recent_low < prev_low:
                explanations.append("Bearish Break of Structure: Price broke below recent swing low")

        # Return result with deduplication
        if explanations:
            # Remove duplicate explanations
            unique_explanations = []
            for exp in explanations:
                if exp not in unique_explanations:
                    unique_explanations.append(exp)
            return True, " | ".join(unique_explanations)

        return False, ""

    def analyze_smc(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Analyze Smart Money Concepts (SMC) using advanced market structure and institutional behavior analysis.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Tuple of (concepts_met, explanation)
        """
        if len(df) < 50:
            return False, ""

        current_price = df['close'].iloc[-1]
        explanations = []

        # 1. Enhanced Break of Structure (BOS) Detection
        # Find swing points for market structure analysis
        swing_highs = []
        swing_lows = []

        # Use a 7-period window for more reliable swing detection
        for i in range(3, len(df) - 3):
            # Swing high: higher than 3 periods before and after
            if (df['high'].iloc[i] == df['high'].iloc[i-3:i+4].max()):
                swing_highs.append((i, df['high'].iloc[i]))

            # Swing low: lower than 3 periods before and after
            if (df['low'].iloc[i] == df['low'].iloc[i-3:i+4].min()):
                swing_lows.append((i, df['low'].iloc[i]))

        # Check for bullish break of structure (higher highs and higher lows)
        if len(swing_highs) >= 3 and len(swing_lows) >= 3:
            recent_highs = swing_highs[-3:]
            recent_lows = swing_lows[-3:]

            # Bullish BOS: Each high is higher than the previous, each low is higher
            if (recent_highs[-1][1] > recent_highs[-2][1] > recent_highs[-3][1] and
                recent_lows[-1][1] > recent_lows[-2][1]):
                explanations.append("Bullish Break of Structure: Clear higher highs and higher lows pattern")

            # Bearish BOS: Each high is lower than the previous, each low is lower
            elif (recent_highs[-1][1] < recent_highs[-2][1] < recent_highs[-3][1] and
                  recent_lows[-1][1] < recent_lows[-2][1]):
                explanations.append("Bearish Break of Structure: Clear lower highs and lower lows pattern")

        # 2. Enhanced Order Block Detection
        # Look for institutional footprints - high volume candles that create imbalances
        df['volume_ma'] = df['volume'].rolling(20).mean()
        df['is_high_volume'] = df['volume'] > (df['volume_ma'] * 2.0)  # 100% above average

        # Find potential order blocks in the last 15 candles
        for i in range(max(0, len(df) - 15), len(df) - 2):
            if not df['is_high_volume'].iloc[i]:
                continue

            candle_high = df['high'].iloc[i]
            candle_low = df['low'].iloc[i]
            candle_close = df['close'].iloc[i]
            candle_open = df['open'].iloc[i]

            # Bullish Order Block: High volume down candle followed by upward move
            if candle_close < candle_open:  # Down candle
                # Check if price moved up significantly after this candle
                future_high = df['high'].iloc[i+1:].max()
                if future_high > candle_high * 1.02:  # 2% move up
                    # Check if current price is near the order block
                    if candle_low * 0.995 <= current_price <= candle_high * 1.005:
                        explanations.append(f"Bullish Order Block: High volume selling at {candle_close:.6f} created buying opportunity")

            # Bearish Order Block: High volume up candle followed by downward move
            elif candle_close > candle_open:  # Up candle
                # Check if price moved down significantly after this candle
                future_low = df['low'].iloc[i+1:].min()
                if future_low < candle_low * 0.98:  # 2% move down
                    # Check if current price is near the order block
                    if candle_low * 0.995 <= current_price <= candle_high * 1.005:
                        explanations.append(f"Bearish Order Block: High volume buying at {candle_close:.6f} created selling opportunity")

        # Return result with deduplication
        if explanations:
            # Remove duplicate explanations
            unique_explanations = []
            for exp in explanations:
                if exp not in unique_explanations:
                    unique_explanations.append(exp)
            return True, " | ".join(unique_explanations)

        return False, ""

    def analyze_price_action(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Analyze Price Action (PA) using pandas-ta.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Tuple of (concepts_met, explanation)
        """
        # Simplified implementation
        if len(df) < 20:
            return False, ""

        # Manual check for engulfing patterns
        for i in range(len(df) - 2, len(df) - 1):
            if i < 0:
                continue

            # Check for bullish engulfing
            if (df['open'].iloc[i] > df['close'].iloc[i] and  # Previous candle is bearish
                df['open'].iloc[i+1] < df['close'].iloc[i+1] and  # Current candle is bullish
                df['open'].iloc[i+1] < df['open'].iloc[i] and  # Current open below previous open
                df['close'].iloc[i+1] > df['close'].iloc[i]):  # Current close above previous close

                explanation = "Bullish Engulfing Pattern detected: Strong reversal signal."
                return True, explanation

            # Check for bearish engulfing
            if (df['open'].iloc[i] < df['close'].iloc[i] and  # Previous candle is bullish
                df['open'].iloc[i+1] > df['close'].iloc[i+1] and  # Current candle is bearish
                df['open'].iloc[i+1] > df['open'].iloc[i] and  # Current open above previous open
                df['close'].iloc[i+1] < df['close'].iloc[i]):  # Current close below previous close

                explanation = "Bearish Engulfing Pattern detected: Strong reversal signal."
                return True, explanation

        # Manual check for hammer/hanging man
        for i in range(len(df) - 2, len(df) - 1):
            if i < 0:
                continue

            body_size = abs(df['close'].iloc[i] - df['open'].iloc[i])
            upper_wick = df['high'].iloc[i] - max(df['open'].iloc[i], df['close'].iloc[i])
            lower_wick = min(df['open'].iloc[i], df['close'].iloc[i]) - df['low'].iloc[i]

            # Hammer: small body, little or no upper wick, long lower wick
            if (body_size < (df['high'].iloc[i] - df['low'].iloc[i]) * 0.3 and  # Small body
                upper_wick < body_size * 0.5 and  # Little or no upper wick
                lower_wick > body_size * 2):  # Long lower wick

                # Hammer in downtrend
                if df['close'].iloc[i-5:i].mean() > df['close'].iloc[i]:
                    explanation = "Bullish Hammer detected: Potential reversal from downtrend."
                    return True, explanation
                # Hanging man in uptrend
                else:
                    explanation = "Bearish Hanging Man detected: Potential reversal from uptrend."
                    return True, explanation

        # Manual check for doji
        for i in range(len(df) - 2, len(df) - 1):
            if i < 0:
                continue

            body_size = abs(df['close'].iloc[i] - df['open'].iloc[i])
            candle_range = df['high'].iloc[i] - df['low'].iloc[i]

            # Doji: very small body compared to range
            if body_size < candle_range * 0.1:
                explanation = "Doji Pattern detected: Market indecision, potential reversal."
                return True, explanation

        # Calculate and check for pin bars manually if not detected by TA-Lib
        for i in range(len(df) - 5, len(df) - 1):
            if i < 0:
                continue

            body_size = abs(df['close'].iloc[i] - df['open'].iloc[i])
            upper_wick = df['high'].iloc[i] - max(df['open'].iloc[i], df['close'].iloc[i])
            lower_wick = min(df['open'].iloc[i], df['close'].iloc[i]) - df['low'].iloc[i]

            # Bullish pin bar
            if lower_wick > 2 * body_size and upper_wick < body_size:
                explanation = "Bullish Pin Bar detected: Rejection of lower prices."
                return True, explanation

            # Bearish pin bar
            if upper_wick > 2 * body_size and lower_wick < body_size:
                explanation = "Bearish Pin Bar detected: Rejection of higher prices."
                return True, explanation

        return False, ""

    def analyze_support_resistance(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Analyze Support and Resistance levels using pandas-ta.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Tuple of (concepts_met, explanation)
        """
        # Simplified implementation
        if len(df) < 50:
            return False, ""

        # Calculate pivot points with error handling
        try:
            df.ta.bbands(append=True)  # Bollinger Bands
        except Exception as e:
            logger.warning(f"Error calculating Bollinger Bands: {e}")
            # Calculate Bollinger Bands manually
            df['BBM_5_2.0'] = df['close'].rolling(window=5).mean()
            df['BBU_5_2.0'] = df['BBM_5_2.0'] + (df['close'].rolling(window=5).std() * 2)
            df['BBL_5_2.0'] = df['BBM_5_2.0'] - (df['close'].rolling(window=5).std() * 2)

        try:
            df.ta.sma(length=50, append=True)  # 50-period SMA
        except Exception as e:
            logger.warning(f"Error calculating SMA_50: {e}")
            # Calculate SMA manually if pandas-ta fails
            df['SMA_50'] = df['close'].rolling(window=50).mean()

        try:
            df.ta.sma(length=200, append=True)  # 200-period SMA
        except Exception as e:
            logger.warning(f"Error calculating SMA_200: {e}")
            # Calculate SMA manually if pandas-ta fails
            df['SMA_200'] = df['close'].rolling(window=200).mean()

        # Current price
        current_price = df['close'].iloc[-1]

        # Check if all required columns exist
        required_columns = ['BBU_5_2.0', 'BBL_5_2.0', 'SMA_50', 'SMA_200']
        for col in required_columns:
            if col not in df.columns:
                logger.warning(f"Required column {col} not found in DataFrame")
                return False, ""

        # Check if price is near Bollinger Bands
        upper_band = df['BBU_5_2.0'].iloc[-1]
        lower_band = df['BBL_5_2.0'].iloc[-1]

        # Check if price is near key moving averages
        sma_50 = df['SMA_50'].iloc[-1]
        sma_200 = df['SMA_200'].iloc[-1]

        # Check for support/resistance at Bollinger Bands
        if abs(current_price - upper_band) / upper_band < 0.005:  # Within 0.5% of upper band
            explanation = f"Price testing resistance at upper Bollinger Band ({upper_band:.8f})"
            return True, explanation

        if abs(current_price - lower_band) / lower_band < 0.005:  # Within 0.5% of lower band
            explanation = f"Price testing support at lower Bollinger Band ({lower_band:.8f})"
            return True, explanation

        # Check for support/resistance at moving averages
        if abs(current_price - sma_50) / sma_50 < 0.005:  # Within 0.5% of 50 SMA
            if current_price > sma_50:
                explanation = f"Price finding support at 50-period SMA ({sma_50:.8f})"
            else:
                explanation = f"Price finding resistance at 50-period SMA ({sma_50:.8f})"
            return True, explanation

        if abs(current_price - sma_200) / sma_200 < 0.005:  # Within 0.5% of 200 SMA
            if current_price > sma_200:
                explanation = f"Price finding support at 200-period SMA ({sma_200:.8f})"
            else:
                explanation = f"Price finding resistance at 200-period SMA ({sma_200:.8f})"
            return True, explanation

        # Find swing highs and lows
        df['swing_high'] = df['high'].rolling(5, center=True).apply(
            lambda x: 1 if x.iloc[2] == max(x) else 0, raw=False
        )
        df['swing_low'] = df['low'].rolling(5, center=True).apply(
            lambda x: 1 if x.iloc[2] == min(x) else 0, raw=False
        )

        # Get recent swing points
        recent_swing_highs = df[df['swing_high'] == 1].iloc[-10:]
        recent_swing_lows = df[df['swing_low'] == 1].iloc[-10:]

        # Check if current price is near any swing high/low
        for _, row in recent_swing_highs.iterrows():
            level_price = row['high']
            if abs(current_price - level_price) / level_price < 0.01:  # Within 1% of level
                if current_price < level_price:
                    explanation = f"Price rejected from resistance level at {level_price:.8f}"
                    return True, explanation

        for _, row in recent_swing_lows.iterrows():
            level_price = row['low']
            if abs(current_price - level_price) / level_price < 0.01:  # Within 1% of level
                if current_price > level_price:
                    explanation = f"Price bounced off support level at {level_price:.8f}"
                    return True, explanation

        return False, ""

    def analyze_patterns(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Analyze advanced chart patterns and technical setups with professional explanations.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Tuple of (concepts_met, explanation)
        """
        if len(df) < 30:
            return False, ""

        current_price = df['close'].iloc[-1]
        explanations = []

        # Calculate technical indicators first
        try:
            df.ta.macd(append=True)
            df.ta.rsi(append=True)
            df.ta.stoch(append=True)
            df.ta.bbands(append=True)
            df.ta.sma(length=20, append=True)
            df.ta.sma(length=50, append=True)
            df.ta.ema(length=12, append=True)
            df.ta.ema(length=26, append=True)
        except Exception as e:
            logger.warning(f"Error calculating indicators: {e}")

        # 1. Advanced Double Top/Bottom Detection
        window_size = 7
        df['local_high'] = df['high'].rolling(window=window_size, center=True).max()
        df['local_low'] = df['low'].rolling(window=window_size, center=True).min()

        # Enhanced Double Bottom Detection (Bullish)
        if len(df) > 30:
            recent_lows = []
            for i in range(len(df) - 30, len(df) - 5):
                if i < 0:
                    continue
                if df['low'].iloc[i] == df['local_low'].iloc[i]:
                    recent_lows.append((i, df['low'].iloc[i]))

            if len(recent_lows) >= 2:
                recent_lows.sort(key=lambda x: x[1])

                # Check for double bottom with specific criteria
                if abs(recent_lows[0][1] - recent_lows[1][1]) / recent_lows[0][1] < 0.015:  # Within 1.5%
                    idx1, idx2 = recent_lows[0][0], recent_lows[1][0]
                    max_between = df['high'].iloc[min(idx1, idx2):max(idx1, idx2)].max()

                    # Check for significant rebound and current price position
                    rebound_strength = (max_between - recent_lows[0][1]) / recent_lows[0][1]
                    if rebound_strength > 0.03 and current_price > recent_lows[0][1] * 1.01:  # 3% rebound, price above lows
                        volume_confirmation = self._check_volume_confirmation(df, idx1, idx2)
                        rsi_divergence = self._check_rsi_divergence(df, idx1, idx2, "bullish")

                        explanation_parts = [
                            f"Double Bottom Pattern: Two lows at {recent_lows[0][1]:.6f} and {recent_lows[1][1]:.6f}",
                            f"Rebound strength: {rebound_strength:.1%} from lows",
                            f"Current price above pattern lows, confirming bullish breakout"
                        ]

                        if volume_confirmation:
                            explanation_parts.append("Volume confirmation: Higher volume on second low")
                        if rsi_divergence:
                            explanation_parts.append("RSI bullish divergence detected")

                        explanations.append(" | ".join(explanation_parts))

        # Enhanced Double Top Detection (Bearish)
        if len(df) > 30:
            recent_highs = []
            for i in range(len(df) - 30, len(df) - 5):
                if i < 0:
                    continue
                if df['high'].iloc[i] == df['local_high'].iloc[i]:
                    recent_highs.append((i, df['high'].iloc[i]))

            if len(recent_highs) >= 2:
                recent_highs.sort(key=lambda x: x[1], reverse=True)

                # Check for double top with specific criteria
                if abs(recent_highs[0][1] - recent_highs[1][1]) / recent_highs[0][1] < 0.015:  # Within 1.5%
                    idx1, idx2 = recent_highs[0][0], recent_highs[1][0]
                    min_between = df['low'].iloc[min(idx1, idx2):max(idx1, idx2)].min()

                    # Check for significant decline and current price position
                    decline_strength = (recent_highs[0][1] - min_between) / recent_highs[0][1]
                    if decline_strength > 0.03 and current_price < recent_highs[0][1] * 0.99:  # 3% decline, price below highs
                        volume_confirmation = self._check_volume_confirmation(df, idx1, idx2)
                        rsi_divergence = self._check_rsi_divergence(df, idx1, idx2, "bearish")

                        explanation_parts = [
                            f"Double Top Pattern: Two highs at {recent_highs[0][1]:.6f} and {recent_highs[1][1]:.6f}",
                            f"Decline strength: {decline_strength:.1%} from highs",
                            f"Current price below pattern highs, confirming bearish breakdown"
                        ]

                        if volume_confirmation:
                            explanation_parts.append("Volume confirmation: Higher volume on second high")
                        if rsi_divergence:
                            explanation_parts.append("RSI bearish divergence detected")

                        explanations.append(" | ".join(explanation_parts))

        # 2. Advanced MACD Analysis
        if 'MACD_12_26_9' in df.columns and 'MACDs_12_26_9' in df.columns:
            macd_line = df['MACD_12_26_9'].iloc[-1]
            macd_signal = df['MACDs_12_26_9'].iloc[-1]
            macd_prev = df['MACD_12_26_9'].iloc[-2]
            signal_prev = df['MACDs_12_26_9'].iloc[-2]

            # MACD Bullish Crossover
            if macd_prev <= signal_prev and macd_line > macd_signal and macd_line < 0:
                momentum_strength = abs(macd_line - macd_signal)
                explanations.append(f"MACD Bullish Crossover: Signal line crossed below MACD in oversold territory (strength: {momentum_strength:.4f})")

            # MACD Bearish Crossover
            elif macd_prev >= signal_prev and macd_line < macd_signal and macd_line > 0:
                momentum_strength = abs(macd_line - macd_signal)
                explanations.append(f"MACD Bearish Crossover: Signal line crossed above MACD in overbought territory (strength: {momentum_strength:.4f})")

            # MACD Zero Line Cross
            elif macd_prev <= 0 and macd_line > 0:
                explanations.append("MACD Zero Line Bullish Cross: Momentum shifting from bearish to bullish")
            elif macd_prev >= 0 and macd_line < 0:
                explanations.append("MACD Zero Line Bearish Cross: Momentum shifting from bullish to bearish")

        # 3. Advanced RSI Analysis
        if 'RSI_14' in df.columns:
            rsi_current = df['RSI_14'].iloc[-1]
            rsi_prev = df['RSI_14'].iloc[-2]

            # RSI Oversold Bounce
            if rsi_current < 35 and rsi_current > rsi_prev and rsi_prev < 30:
                explanations.append(f"RSI Oversold Bounce: RSI recovering from oversold levels ({rsi_current:.1f}), potential bullish reversal")

            # RSI Overbought Rejection
            elif rsi_current > 65 and rsi_current < rsi_prev and rsi_prev > 70:
                explanations.append(f"RSI Overbought Rejection: RSI declining from overbought levels ({rsi_current:.1f}), potential bearish reversal")

            # RSI 50 Line Cross
            elif rsi_prev <= 50 and rsi_current > 50:
                explanations.append("RSI Bullish 50 Cross: RSI crossed above 50, confirming bullish momentum")
            elif rsi_prev >= 50 and rsi_current < 50:
                explanations.append("RSI Bearish 50 Cross: RSI crossed below 50, confirming bearish momentum")

        # 4. Bollinger Bands Squeeze and Expansion
        if 'BBU_5_2.0' in df.columns and 'BBL_5_2.0' in df.columns:
            bb_upper = df['BBU_5_2.0'].iloc[-1]
            bb_lower = df['BBL_5_2.0'].iloc[-1]
            bb_width = (bb_upper - bb_lower) / df['BBM_5_2.0'].iloc[-1]
            bb_width_prev = (df['BBU_5_2.0'].iloc[-5] - df['BBL_5_2.0'].iloc[-5]) / df['BBM_5_2.0'].iloc[-5]

            # Bollinger Band Squeeze
            if bb_width < 0.04 and bb_width < bb_width_prev:  # Bands tightening
                explanations.append(f"Bollinger Band Squeeze: Volatility compression ({bb_width:.1%}), expecting breakout")

            # Bollinger Band Expansion
            elif bb_width > bb_width_prev * 1.2:  # Bands expanding
                if current_price > bb_upper:
                    explanations.append("Bollinger Band Breakout: Price above upper band, strong bullish momentum")
                elif current_price < bb_lower:
                    explanations.append("Bollinger Band Breakdown: Price below lower band, strong bearish momentum")

        # 5. Moving Average Confluence
        if 'SMA_20' in df.columns and 'SMA_50' in df.columns:
            sma_20 = df['SMA_20'].iloc[-1]
            sma_50 = df['SMA_50'].iloc[-1]
            sma_20_prev = df['SMA_20'].iloc[-2]
            sma_50_prev = df['SMA_50'].iloc[-2]

            # Golden Cross
            if sma_20_prev <= sma_50_prev and sma_20 > sma_50:
                explanations.append("Golden Cross: 20 SMA crossed above 50 SMA, bullish trend confirmation")

            # Death Cross
            elif sma_20_prev >= sma_50_prev and sma_20 < sma_50:
                explanations.append("Death Cross: 20 SMA crossed below 50 SMA, bearish trend confirmation")

            # Price vs MA Analysis
            if current_price > sma_20 > sma_50:
                ma_strength = (current_price - sma_50) / sma_50
                explanations.append(f"Bullish MA Alignment: Price above both 20 & 50 SMA (strength: {ma_strength:.1%})")
            elif current_price < sma_20 < sma_50:
                ma_strength = (sma_50 - current_price) / sma_50
                explanations.append(f"Bearish MA Alignment: Price below both 20 & 50 SMA (strength: {ma_strength:.1%})")

        # Return results with deduplication
        if explanations:
            unique_explanations = []
            for exp in explanations:
                if exp not in unique_explanations:
                    unique_explanations.append(exp)
            return True, " | ".join(unique_explanations)

        return False, ""

    def _check_volume_confirmation(self, df: pd.DataFrame, idx1: int, idx2: int) -> bool:
        """
        Check if volume confirms the pattern at the given indices.

        Args:
            df: DataFrame with OHLCV data
            idx1: First pattern point index
            idx2: Second pattern point index

        Returns:
            bool: True if volume confirms the pattern
        """
        try:
            if 'volume' not in df.columns:
                return False

            vol1 = df['volume'].iloc[idx1]
            vol2 = df['volume'].iloc[idx2]
            avg_volume = df['volume'].rolling(20).mean().iloc[idx2]

            # Volume confirmation if second point has higher volume or above average
            return vol2 > vol1 or vol2 > avg_volume * 1.2
        except:
            return False

    def _check_rsi_divergence(self, df: pd.DataFrame, idx1: int, idx2: int, direction: str) -> bool:
        """
        Check for RSI divergence at the given pattern points.

        Args:
            df: DataFrame with OHLCV data
            idx1: First pattern point index
            idx2: Second pattern point index
            direction: "bullish" or "bearish"

        Returns:
            bool: True if RSI divergence is detected
        """
        try:
            if 'RSI_14' not in df.columns:
                return False

            rsi1 = df['RSI_14'].iloc[idx1]
            rsi2 = df['RSI_14'].iloc[idx2]
            price1 = df['low'].iloc[idx1] if direction == "bullish" else df['high'].iloc[idx1]
            price2 = df['low'].iloc[idx2] if direction == "bullish" else df['high'].iloc[idx2]

            if direction == "bullish":
                # Bullish divergence: lower price low but higher RSI low
                return price2 <= price1 and rsi2 > rsi1
            else:
                # Bearish divergence: higher price high but lower RSI high
                return price2 >= price1 and rsi2 < rsi1
        except:
            return False

    def calculate_targets_and_stop(self, df: pd.DataFrame, direction: str) -> Tuple[float, List[float]]:
        """
        Calculate targets and stop loss based on PURE SMC/ICT/PA/S&R technical analysis.
        No percentages, no risk multiples - only actual market structure levels.

        Args:
            df: DataFrame with OHLCV data
            direction: "BUY" or "SELL"

        Returns:
            Tuple of (stop_loss, targets)
        """
        current_price = df['close'].iloc[-1]

        # Get all technical levels first
        technical_levels = self._get_all_technical_levels(df, current_price)

        if direction == "BUY":
            stop_loss = self._find_buy_stop_loss(technical_levels, current_price)
            targets = self._find_buy_targets(technical_levels, current_price)
        else:  # SELL
            stop_loss = self._find_sell_stop_loss(technical_levels, current_price)
            targets = self._find_sell_targets(technical_levels, current_price)

        return stop_loss, targets

    def _get_all_technical_levels(self, df: pd.DataFrame, current_price: float) -> Dict[str, List[float]]:
        """
        Extract all technical levels from SMC/ICT/PA/S&R analysis.
        Returns actual price levels, not percentages.
        """
        levels = {
            'support_levels': [],
            'resistance_levels': [],
            'fvg_support': [],
            'fvg_resistance': [],
            'order_blocks_support': [],
            'order_blocks_resistance': [],
            'swing_highs': [],
            'swing_lows': [],
            'key_levels': []
        }

        # 1. Extract Fair Value Gaps
        for i in range(len(df) - 3, max(len(df) - 20, 0), -1):
            if i < 2 or i+2 >= len(df):
                continue

            # Bullish FVG (Support)
            if df['high'].iloc[i] < df['low'].iloc[i+2]:
                gap_low = df['high'].iloc[i]
                gap_high = df['low'].iloc[i+2]
                levels['fvg_support'].extend([gap_low, gap_high])

            # Bearish FVG (Resistance)
            elif df['low'].iloc[i] > df['high'].iloc[i+2]:
                gap_high = df['low'].iloc[i]
                gap_low = df['high'].iloc[i+2]
                levels['fvg_resistance'].extend([gap_low, gap_high])

        # 2. Extract Order Blocks (High volume levels)
        volume_threshold = df['volume'].quantile(0.8)
        volume_spikes = df[df['volume'] > volume_threshold].iloc[-15:]

        for idx, spike_row in volume_spikes.iterrows():
            spike_high = spike_row['high']
            spike_low = spike_row['low']

            if spike_high < current_price:
                levels['order_blocks_support'].extend([spike_low, spike_high])
            else:
                levels['order_blocks_resistance'].extend([spike_low, spike_high])

        # 3. Extract Swing Highs and Lows
        df['swing_high'] = df['high'].rolling(5, center=True).apply(
            lambda x: 1 if x.iloc[2] == max(x) else 0, raw=False
        )
        df['swing_low'] = df['low'].rolling(5, center=True).apply(
            lambda x: 1 if x.iloc[2] == min(x) else 0, raw=False
        )

        swing_highs = df[df['swing_high'] == 1]['high'].tolist()
        swing_lows = df[df['swing_low'] == 1]['low'].tolist()

        levels['swing_highs'] = swing_highs
        levels['swing_lows'] = swing_lows

        # 4. Extract Support/Resistance from moving averages and key levels
        try:
            df['SMA_50'] = df['close'].rolling(50).mean()
            df['SMA_200'] = df['close'].rolling(200).mean()
            df['EMA_21'] = df['close'].ewm(span=21).mean()

            sma_50 = df['SMA_50'].iloc[-1]
            sma_200 = df['SMA_200'].iloc[-1]
            ema_21 = df['EMA_21'].iloc[-1]

            if sma_50 < current_price:
                levels['support_levels'].append(sma_50)
            else:
                levels['resistance_levels'].append(sma_50)

            if sma_200 < current_price:
                levels['support_levels'].append(sma_200)
            else:
                levels['resistance_levels'].append(sma_200)

            if ema_21 < current_price:
                levels['support_levels'].append(ema_21)
            else:
                levels['resistance_levels'].append(ema_21)

        except Exception as e:
            logger.warning(f"Error calculating moving averages: {e}")

        # 5. Extract Bollinger Bands levels
        try:
            df['BB_upper'] = df['close'].rolling(20).mean() + (df['close'].rolling(20).std() * 2)
            df['BB_lower'] = df['close'].rolling(20).mean() - (df['close'].rolling(20).std() * 2)

            bb_upper = df['BB_upper'].iloc[-1]
            bb_lower = df['BB_lower'].iloc[-1]

            levels['resistance_levels'].append(bb_upper)
            levels['support_levels'].append(bb_lower)

        except Exception as e:
            logger.warning(f"Error calculating Bollinger Bands: {e}")

        return levels

    def _find_buy_stop_loss(self, technical_levels: Dict[str, List[float]], current_price: float) -> float:
        """
        Find stop loss for BUY trades using PURE technical analysis.
        Priority: Swing Lows > Order Block Support > FVG Support > Key Support Levels
        """
        # Priority 1: Recent swing lows below current price
        valid_swing_lows = [level for level in technical_levels['swing_lows']
                           if level < current_price]
        if valid_swing_lows:
            # Use the highest swing low below current price (closest support)
            stop_loss = max(valid_swing_lows)
            logger.info(f"BUY Stop Loss: Using swing low at {stop_loss}")
            return stop_loss

        # Priority 2: Order block support levels
        valid_ob_support = [level for level in technical_levels['order_blocks_support']
                           if level < current_price]
        if valid_ob_support:
            stop_loss = max(valid_ob_support)
            logger.info(f"BUY Stop Loss: Using order block support at {stop_loss}")
            return stop_loss

        # Priority 3: FVG support levels
        valid_fvg_support = [level for level in technical_levels['fvg_support']
                            if level < current_price]
        if valid_fvg_support:
            stop_loss = max(valid_fvg_support)
            logger.info(f"BUY Stop Loss: Using FVG support at {stop_loss}")
            return stop_loss

        # Priority 4: Key support levels (MA, BB lower)
        valid_support = [level for level in technical_levels['support_levels']
                        if level < current_price]
        if valid_support:
            stop_loss = max(valid_support)
            logger.info(f"BUY Stop Loss: Using key support at {stop_loss}")
            return stop_loss

        # Fallback: Use a basic technical level
        stop_loss = current_price * 0.98  # 2% fallback only if no technical levels found
        logger.warning(f"BUY Stop Loss: No technical levels found, using fallback at {stop_loss}")
        return stop_loss

    def _find_sell_stop_loss(self, technical_levels: Dict[str, List[float]], current_price: float) -> float:
        """
        Find stop loss for SELL trades using PURE technical analysis.
        Priority: Swing Highs > Order Block Resistance > FVG Resistance > Key Resistance Levels
        """
        # Priority 1: Recent swing highs above current price
        valid_swing_highs = [level for level in technical_levels['swing_highs']
                            if level > current_price]
        if valid_swing_highs:
            # Use the lowest swing high above current price (closest resistance)
            stop_loss = min(valid_swing_highs)
            logger.info(f"SELL Stop Loss: Using swing high at {stop_loss}")
            return stop_loss

        # Priority 2: Order block resistance levels
        valid_ob_resistance = [level for level in technical_levels['order_blocks_resistance']
                              if level > current_price]
        if valid_ob_resistance:
            stop_loss = min(valid_ob_resistance)
            logger.info(f"SELL Stop Loss: Using order block resistance at {stop_loss}")
            return stop_loss

        # Priority 3: FVG resistance levels
        valid_fvg_resistance = [level for level in technical_levels['fvg_resistance']
                               if level > current_price]
        if valid_fvg_resistance:
            stop_loss = min(valid_fvg_resistance)
            logger.info(f"SELL Stop Loss: Using FVG resistance at {stop_loss}")
            return stop_loss

        # Priority 4: Key resistance levels (MA, BB upper)
        valid_resistance = [level for level in technical_levels['resistance_levels']
                           if level > current_price]
        if valid_resistance:
            stop_loss = min(valid_resistance)
            logger.info(f"SELL Stop Loss: Using key resistance at {stop_loss}")
            return stop_loss

        # Fallback: Use a basic technical level
        stop_loss = current_price * 1.02  # 2% fallback only if no technical levels found
        logger.warning(f"SELL Stop Loss: No technical levels found, using fallback at {stop_loss}")
        return stop_loss

    def _find_buy_targets(self, technical_levels: Dict[str, List[float]], current_price: float) -> List[float]:
        """
        Find targets for BUY trades using PURE technical analysis.
        Priority: Swing Highs > Order Block Resistance > FVG Resistance > Key Resistance Levels
        """
        all_resistance_levels = []

        # Collect all resistance levels above current price
        all_resistance_levels.extend([level for level in technical_levels['swing_highs']
                                     if level > current_price])
        all_resistance_levels.extend([level for level in technical_levels['order_blocks_resistance']
                                     if level > current_price])
        all_resistance_levels.extend([level for level in technical_levels['fvg_resistance']
                                     if level > current_price])
        all_resistance_levels.extend([level for level in technical_levels['resistance_levels']
                                     if level > current_price])

        if not all_resistance_levels:
            # Fallback: Use basic levels if no technical levels found
            targets = [current_price * 1.02, current_price * 1.04, current_price * 1.06]
            logger.warning(f"BUY Targets: No technical levels found, using fallback targets")
            return targets

        # Sort resistance levels by distance from current price (closest first)
        sorted_levels = sorted(set(all_resistance_levels))

        # Take the first 3 levels as targets, ensuring they're spaced properly
        targets = []
        for level in sorted_levels:
            if not targets or (level - targets[-1]) / current_price > 0.005:  # At least 0.5% spacing
                targets.append(level)
            if len(targets) >= 3:
                break

        # If we don't have 3 targets, add calculated ones based on the last target
        while len(targets) < 3:
            if targets:
                next_target = targets[-1] * 1.015  # 1.5% above last target
            else:
                next_target = current_price * 1.02
            targets.append(next_target)

        logger.info(f"BUY Targets: Using technical resistance levels {targets[:3]}")
        return targets[:3]

    def _find_sell_targets(self, technical_levels: Dict[str, List[float]], current_price: float) -> List[float]:
        """
        Find targets for SELL trades using PURE technical analysis.
        Priority: Swing Lows > Order Block Support > FVG Support > Key Support Levels
        """
        all_support_levels = []

        # Collect all support levels below current price
        all_support_levels.extend([level for level in technical_levels['swing_lows']
                                  if level < current_price])
        all_support_levels.extend([level for level in technical_levels['order_blocks_support']
                                  if level < current_price])
        all_support_levels.extend([level for level in technical_levels['fvg_support']
                                  if level < current_price])
        all_support_levels.extend([level for level in technical_levels['support_levels']
                                  if level < current_price])

        if not all_support_levels:
            # Fallback: Use basic levels if no technical levels found
            targets = [current_price * 0.98, current_price * 0.96, current_price * 0.94]
            logger.warning(f"SELL Targets: No technical levels found, using fallback targets")
            return targets

        # Sort support levels by distance from current price (closest first, descending)
        sorted_levels = sorted(set(all_support_levels), reverse=True)

        # Take the first 3 levels as targets, ensuring they're spaced properly
        targets = []
        for level in sorted_levels:
            if not targets or (targets[-1] - level) / current_price > 0.005:  # At least 0.5% spacing
                targets.append(level)
            if len(targets) >= 3:
                break

        # If we don't have 3 targets, add calculated ones based on the last target
        while len(targets) < 3:
            if targets:
                next_target = targets[-1] * 0.985  # 1.5% below last target
            else:
                next_target = current_price * 0.98
            targets.append(next_target)

        logger.info(f"SELL Targets: Using technical support levels {targets[:3]}")
        return targets[:3]

    def generate_chart(self, df: pd.DataFrame, symbol: str) -> Optional[BytesIO]:
        """
        Generate a chart image for the trading signal.

        Args:
            df: DataFrame with OHLCV data
            symbol: The trading pair symbol

        Returns:
            BytesIO object containing the chart image
        """
        try:
            # Create a figure
            plt.figure(figsize=(12, 8))

            # Plot candlestick chart
            plt.subplot(2, 1, 1)

            # Plot OHLC
            plt.plot(df.index, df['close'], label='Close')

            # Plot indicators
            if 'BBU_5_2.0' in df.columns:
                plt.plot(df.index, df['BBU_5_2.0'], 'r--', label='Upper BB')
                plt.plot(df.index, df['BBM_5_2.0'], 'g--', label='Middle BB')
                plt.plot(df.index, df['BBL_5_2.0'], 'r--', label='Lower BB')

            if 'SMA_50' in df.columns:
                plt.plot(df.index, df['SMA_50'], 'b-', label='SMA 50')

            if 'SMA_200' in df.columns:
                plt.plot(df.index, df['SMA_200'], 'y-', label='SMA 200')

            plt.title(f'{symbol} Price Chart')
            plt.ylabel('Price')
            plt.legend()
            plt.grid(True)

            # Plot volume
            plt.subplot(2, 1, 2)
            plt.bar(df.index, df['volume'], color='blue', alpha=0.5)
            plt.title('Volume')
            plt.ylabel('Volume')
            plt.grid(True)

            plt.tight_layout()

            # Save to BytesIO
            buf = BytesIO()
            plt.savefig(buf, format='png')
            buf.seek(0)
            plt.close()

            return buf

        except Exception as e:
            logger.error(f"Error generating chart: {e}")
            return None

    def get_current_price(self, symbol: str) -> float:
        """
        Get the current price for a symbol.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")

        Returns:
            Current price as a float
        """
        try:
            # Get the latest OHLCV data
            df = self.exchange.get_ohlcv(symbol, "1m", 1)

            if df is None or df.empty:
                # If no data, try to get ticker directly
                ticker = self.exchange.get_ticker(symbol)
                if ticker and 'last' in ticker:
                    return float(ticker['last'])
                else:
                    raise ValueError(f"Could not get price data for {symbol}")

            # Return the latest close price
            return float(df['close'].iloc[-1])
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            # Return a default value or raise an exception
            raise ValueError(f"Could not get current price for {symbol}: {e}")

    def calculate_volatility(self, symbol: str) -> float:
        """
        Calculate the volatility of a symbol based on recent price action.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT')

        Returns:
            float: Volatility as a decimal (e.g., 0.05 for 5% volatility)
        """
        try:
            # Get historical data for multiple timeframes
            timeframes = ['1h', '4h', '1d']
            volatilities = []

            for tf in timeframes:
                # Get OHLCV data
                df = self.exchange.get_ohlcv(symbol, tf, limit=20)

                if df is not None and not df.empty and len(df) >= 10:
                    # Calculate daily returns
                    df['returns'] = df['close'].pct_change()

                    # Calculate volatility as standard deviation of returns
                    volatility = df['returns'].std()
                    volatilities.append(volatility)

            # Average volatility across timeframes
            if volatilities:
                avg_volatility = sum(volatilities) / len(volatilities)
                return avg_volatility
            else:
                # Default volatility if calculation fails
                return 0.04  # 4% default volatility

        except Exception as e:
            logger.error(f"Error calculating volatility for {symbol}: {e}")
            return 0.04  # 4% default volatility

    def calculate_stop_loss(self, symbol: str, direction: str) -> float:
        """
        Calculate stop loss for a symbol based on professional market structure analysis.
        Uses swing highs/lows, key moving averages, and ATR for optimal placement.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")
            direction: "BUY" or "SELL"

        Returns:
            Stop loss price as a float
        """
        try:
            # Get OHLCV data
            df = self.exchange.get_ohlcv(symbol, "1h", 100)

            if df is None or df.empty:
                raise ValueError(f"Could not get OHLCV data for {symbol}")

            # Use the advanced market structure method
            stop_loss, _ = self.calculate_targets_and_stop(df, direction)
            return float(stop_loss)

        except Exception as e:
            logger.error(f"Error calculating stop loss for {symbol}: {e}")
            # Fallback: Use basic technical analysis if advanced method fails
            try:
                current_price = self.get_current_price(symbol)
                df = self.exchange.get_ohlcv(symbol, "1h", 50)

                if df is not None and not df.empty:
                    # Calculate basic swing points for stop loss
                    df['swing_high'] = df['high'].rolling(5, center=True).apply(
                        lambda x: 1 if x.iloc[2] == max(x) else 0, raw=False
                    )
                    df['swing_low'] = df['low'].rolling(5, center=True).apply(
                        lambda x: 1 if x.iloc[2] == min(x) else 0, raw=False
                    )

                    if direction == "BUY":
                        # Find recent swing low for stop
                        swing_lows = df[df['swing_low'] == 1]['low'].tolist()
                        valid_lows = [low for low in swing_lows if low < current_price]
                        if valid_lows:
                            return float(max(valid_lows))  # Closest swing low
                    else:
                        # Find recent swing high for stop
                        swing_highs = df[df['swing_high'] == 1]['high'].tolist()
                        valid_highs = [high for high in swing_highs if high > current_price]
                        if valid_highs:
                            return float(min(valid_highs))  # Closest swing high

                # If no swing points found, use basic technical fallback
                current_price = self.get_current_price(symbol)
                if direction == "BUY":
                    return current_price * 0.98  # 2% technical fallback
                else:
                    return current_price * 1.02  # 2% technical fallback

            except Exception as e2:
                logger.error(f"Fallback stop loss calculation failed for {symbol}: {e2}")
                # Emergency technical fallback
                current_price = self.get_current_price(symbol)
                if direction == "BUY":
                    return current_price * 0.97  # 3% emergency technical stop
                else:
                    return current_price * 1.03  # 3% emergency technical stop

    def calculate_targets(self, symbol: str, direction: str) -> List[float]:
        """
        Calculate price targets for a symbol based on PURE SMC/ICT market structure analysis.
        Uses actual swing highs/lows, order blocks, and technical levels - NO risk/reward ratios.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")
            direction: "BUY" or "SELL"

        Returns:
            List of target prices
        """
        try:
            # Get OHLCV data
            df = self.exchange.get_ohlcv(symbol, "1h", 100)

            if df is None or df.empty:
                raise ValueError(f"Could not get OHLCV data for {symbol}")

            # Use the advanced market structure method
            _, targets = self.calculate_targets_and_stop(df, direction)
            return [float(target) for target in targets]

        except Exception as e:
            logger.error(f"Error calculating targets for {symbol}: {e}")
            # Fallback: Use basic technical analysis if advanced method fails
            try:
                current_price = self.get_current_price(symbol)
                df = self.exchange.get_ohlcv(symbol, "1h", 50)

                if df is not None and not df.empty:
                    # Calculate basic swing points for targets
                    df['swing_high'] = df['high'].rolling(5, center=True).apply(
                        lambda x: 1 if x.iloc[2] == max(x) else 0, raw=False
                    )
                    df['swing_low'] = df['low'].rolling(5, center=True).apply(
                        lambda x: 1 if x.iloc[2] == min(x) else 0, raw=False
                    )

                    # Calculate Bollinger Bands for technical levels
                    df['bb_middle'] = df['close'].rolling(20).mean()
                    df['bb_std'] = df['close'].rolling(20).std()
                    df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
                    df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)

                    if direction == "BUY":
                        # Find swing highs for resistance targets
                        swing_highs = df[df['swing_high'] == 1]['high'].tolist()
                        valid_highs = [high for high in swing_highs if high > current_price]
                        bb_upper = df['bb_upper'].iloc[-1]

                        if valid_highs:
                            # Use actual swing highs as targets
                            sorted_highs = sorted(valid_highs)[:3]
                            targets = sorted_highs
                            # Fill with BB upper if needed
                            while len(targets) < 3:
                                targets.append(bb_upper * (1 + 0.01 * len(targets)))
                        else:
                            # Use technical levels
                            targets = [bb_upper, bb_upper * 1.02, bb_upper * 1.04]
                    else:
                        # Find swing lows for support targets
                        swing_lows = df[df['swing_low'] == 1]['low'].tolist()
                        valid_lows = [low for low in swing_lows if low < current_price]
                        bb_lower = df['bb_lower'].iloc[-1]

                        if valid_lows:
                            # Use actual swing lows as targets
                            sorted_lows = sorted(valid_lows, reverse=True)[:3]
                            targets = sorted_lows
                            # Fill with BB lower if needed
                            while len(targets) < 3:
                                targets.append(bb_lower * (1 - 0.01 * len(targets)))
                        else:
                            # Use technical levels
                            targets = [bb_lower, bb_lower * 0.98, bb_lower * 0.96]

                    return [float(target) for target in targets]
                else:
                    # Last resort: basic technical levels
                    current_price = self.get_current_price(symbol)
                    if direction == "BUY":
                        return [current_price * 1.02, current_price * 1.04, current_price * 1.06]  # Basic technical levels
                    else:
                        return [current_price * 0.98, current_price * 0.96, current_price * 0.94]  # Basic technical levels

            except Exception as e2:
                logger.error(f"Fallback target calculation failed for {symbol}: {e2}")
                # Emergency technical fallback
                current_price = self.get_current_price(symbol)
                if direction == "BUY":
                    return [current_price * 1.015, current_price * 1.03, current_price * 1.045]  # Emergency technical levels
                else:
                    return [current_price * 0.985, current_price * 0.97, current_price * 0.955]  # Emergency technical levels

    def determine_signal_direction(self, df: pd.DataFrame, analysis_results: Dict[str, bool], explanations: List[str]) -> str:
        """
        Determine whether to generate a BUY or SELL signal based on analysis results.

        Args:
            df: DataFrame with OHLCV data
            analysis_results: Dictionary with analysis results
            explanations: List of explanations from analysis

        Returns:
            "BUY", "SELL", or "NONE"
        """
        current_price = df['close'].iloc[-1]
        bullish_signals = 0
        bearish_signals = 0

        # Analyze explanations for directional bias with weighted scoring
        combined_explanations = " ".join(explanations).lower()

        # Define weighted bullish indicators (higher weight = stronger signal)
        bullish_indicators = {
            "bullish fair value gap": 3,
            "bullish liquidity sweep": 3,
            "bullish order block": 3,
            "bullish break of structure": 4,
            "demand zone": 2,
            "support": 2,
            "higher highs": 2,
            "higher lows": 2,
            "buying": 1,
            "upward": 1,
            "above": 1
        }

        # Define weighted bearish indicators (higher weight = stronger signal)
        bearish_indicators = {
            "bearish fair value gap": 3,
            "bearish liquidity sweep": 3,
            "bearish order block": 3,
            "bearish break of structure": 4,
            "supply zone": 2,
            "resistance": 2,
            "lower highs": 2,
            "lower lows": 2,
            "selling": 1,
            "downward": 1,
            "below": 1
        }

        # Count weighted signals
        for indicator, weight in bullish_indicators.items():
            if indicator in combined_explanations:
                bullish_signals += weight

        for indicator, weight in bearish_indicators.items():
            if indicator in combined_explanations:
                bearish_signals += weight

        # Additional technical analysis for direction
        try:
            # Calculate moving averages for trend direction
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()

            sma_20 = df['sma_20'].iloc[-1]
            sma_50 = df['sma_50'].iloc[-1]

            # Price above moving averages = bullish bias
            if current_price > sma_20 > sma_50:
                bullish_signals += 3  # Strong bullish trend
            elif current_price < sma_20 < sma_50:
                bearish_signals += 3  # Strong bearish trend
            elif current_price > sma_20:
                bullish_signals += 1  # Mild bullish
            elif current_price < sma_20:
                bearish_signals += 1  # Mild bearish

            # Check recent price momentum
            recent_change = (current_price - df['close'].iloc[-5]) / df['close'].iloc[-5]
            if recent_change > 0.02:  # 2% up in last 5 periods
                bullish_signals += 2
            elif recent_change > 0.01:  # 1% up
                bullish_signals += 1
            elif recent_change < -0.02:  # 2% down
                bearish_signals += 2
            elif recent_change < -0.01:  # 1% down
                bearish_signals += 1

        except Exception as e:
            logger.warning(f"Error in directional analysis: {e}")

        # Log the signal analysis for debugging
        logger.info(f"Signal analysis - Bullish: {bullish_signals}, Bearish: {bearish_signals}")
        logger.info(f"Explanations analyzed: {combined_explanations}")

        # Determine final direction with higher threshold to avoid conflicting signals
        signal_difference = abs(bullish_signals - bearish_signals)

        # Require a clear winner with at least 2 point difference and minimum 4 points
        if bullish_signals >= 4 and bullish_signals > bearish_signals and signal_difference >= 2:
            return "BUY"
        elif bearish_signals >= 4 and bearish_signals > bullish_signals and signal_difference >= 2:
            return "SELL"
        else:
            # If signals are conflicting or weak, return NONE
            logger.warning(f"Conflicting or weak signals detected. Bullish: {bullish_signals}, Bearish: {bearish_signals}")
            return "NONE"

    def _filter_explanations_by_direction(self, explanations: List[str], direction: str) -> List[str]:
        """
        Filter explanations to remove those that conflict with the determined direction.

        Args:
            explanations: List of all explanations
            direction: Determined signal direction ("BUY", "SELL", or "NONE")

        Returns:
            Filtered list of explanations that align with the direction
        """
        if direction == "NONE":
            return explanations

        filtered = []

        for explanation in explanations:
            explanation_lower = explanation.lower()

            if direction == "BUY":
                # Keep bullish explanations, filter out bearish ones
                if any(bearish_term in explanation_lower for bearish_term in [
                    "bearish", "sell", "resistance", "lower highs", "lower lows",
                    "downward", "selling", "supply", "overbought", "rejection"
                ]):
                    continue  # Skip bearish explanations for BUY signals
                filtered.append(explanation)

            elif direction == "SELL":
                # Keep bearish explanations, filter out bullish ones
                if any(bullish_term in explanation_lower for bullish_term in [
                    "bullish", "buy", "support", "higher highs", "higher lows",
                    "upward", "buying", "demand", "oversold", "bounce"
                ]):
                    continue  # Skip bullish explanations for SELL signals
                filtered.append(explanation)

        return filtered

    def analyze_symbol(self, symbol: str, market_type: str = "SPOT") -> Optional[Dict[str, any]]:
        """
        Analyze a trading pair and generate a signal if conditions are met.

        Args:
            symbol: The trading pair symbol (e.g., "BTC/USDT")
            market_type: The market type ("SPOT" or "FUTURES")

        Returns:
            Dictionary of analysis results if conditions are met, None otherwise
        """
        try:
            # Get OHLCV data
            df = self.exchange.get_ohlcv(symbol, "1h", 100)

            if df is None:
                logger.warning(f"No data returned for {symbol}")
                return None

            if df.empty or len(df) < 50:
                logger.info(f"Insufficient data for {symbol}: {len(df) if not df.empty else 0} rows")
                return None

            # Check if DataFrame has required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.warning(f"Missing required columns for {symbol}: {missing_columns}")
                return None

            # Pre-calculate all indicators that will be needed to avoid KeyError issues
            try:
                # Calculate SMA values
                df['SMA_50'] = df['close'].rolling(window=50).mean()
                df['SMA_200'] = df['close'].rolling(window=200).mean()

                # Calculate Bollinger Bands
                df['BBM_5_2.0'] = df['close'].rolling(window=5).mean()
                df['BBU_5_2.0'] = df['BBM_5_2.0'] + (df['close'].rolling(window=5).std() * 2)
                df['BBL_5_2.0'] = df['BBM_5_2.0'] - (df['close'].rolling(window=5).std() * 2)

                # Calculate RSI
                delta = df['close'].diff()
                gain = delta.where(delta > 0, 0)
                loss = -delta.where(delta < 0, 0)
                avg_gain = gain.rolling(window=14).mean()
                avg_loss = loss.rolling(window=14).mean()
                rs = avg_gain / avg_loss
                df['RSI_14'] = 100 - (100 / (1 + rs))

                # Calculate MACD
                ema12 = df['close'].ewm(span=12, adjust=False).mean()
                ema26 = df['close'].ewm(span=26, adjust=False).mean()
                df['MACD_12_26_9'] = ema12 - ema26
                df['MACDs_12_26_9'] = df['MACD_12_26_9'].ewm(span=9, adjust=False).mean()

                # Calculate ATR
                high_low = df['high'] - df['low']
                high_close = (df['high'] - df['close'].shift()).abs()
                low_close = (df['low'] - df['close'].shift()).abs()
                ranges = pd.concat([high_low, high_close, low_close], axis=1)
                true_range = ranges.max(axis=1)
                df['ATRr_14'] = true_range.rolling(14).mean()
            except Exception as e:
                logger.warning(f"Error pre-calculating indicators for {symbol}: {e}")
                # Calculate essential indicators manually
                try:
                    # Calculate SMA values manually
                    df['SMA_50'] = df['close'].rolling(window=50).mean()
                    df['SMA_200'] = df['close'].rolling(window=200).mean()

                    # Calculate Bollinger Bands manually
                    df['BBM_5_2.0'] = df['close'].rolling(window=5).mean()
                    df['BBU_5_2.0'] = df['BBM_5_2.0'] + (df['close'].rolling(window=5).std() * 2)
                    df['BBL_5_2.0'] = df['BBM_5_2.0'] - (df['close'].rolling(window=5).std() * 2)
                except Exception as e2:
                    logger.error(f"Critical error calculating essential indicators for {symbol}: {e2}")
                    return None

            # Skip high-priced tokens for spot trading
            if market_type == "SPOT":
                current_price = df['close'].iloc[-1]
                if current_price > 20:
                    logger.info(f"Skipping high-priced token {symbol} for SPOT trading")
                    return None

            # Create a dictionary to store analysis results
            analysis_result = {
                "ICT condition": False,
                "SMC condition": False,
                "PA condition": False,
                "S/R condition": False,
                "Pattern recognition condition": False
            }

            # Analyze trading concepts
            concepts_met = []
            explanations = []

            # ICT analysis
            ict_met, ict_explanation = self.analyze_ict(df)
            if ict_met:
                concepts_met.append("ICT")
                explanations.append(ict_explanation)
                analysis_result["ICT condition"] = True

            # SMC analysis
            smc_met, smc_explanation = self.analyze_smc(df)
            if smc_met:
                concepts_met.append("SMC")
                explanations.append(smc_explanation)
                analysis_result["SMC condition"] = True

            # Price Action analysis
            pa_met, pa_explanation = self.analyze_price_action(df)
            if pa_met:
                concepts_met.append("PA")
                explanations.append(pa_explanation)
                analysis_result["PA condition"] = True

            # Support/Resistance analysis
            sr_met, sr_explanation = self.analyze_support_resistance(df)
            if sr_met:
                concepts_met.append("S/R")
                explanations.append(sr_explanation)
                analysis_result["S/R condition"] = True

            # Pattern analysis
            pattern_met, pattern_explanation = self.analyze_patterns(df)
            if pattern_met:
                concepts_met.append("Pattern Recognition")
                explanations.append(pattern_explanation)
                analysis_result["Pattern recognition condition"] = True

            # Determine signal direction
            signal_direction = self.determine_signal_direction(df, analysis_result, explanations)

            # Filter out conflicting explanations based on final direction
            filtered_explanations = self._filter_explanations_by_direction(explanations, signal_direction)

            # Add direction and explanations to result
            analysis_result["direction"] = signal_direction
            analysis_result["explanations"] = filtered_explanations
            analysis_result["concepts_met"] = concepts_met

            # Log which concepts were met for this symbol
            if concepts_met:
                logger.info(f"Symbol {symbol} in {market_type} market met the following concepts: {', '.join(concepts_met)} - Direction: {signal_direction}")

            # Return the analysis results
            return analysis_result

        except Exception as e:
            logger.error(f"Error analyzing symbol {symbol}: {e}")
            return None

    def find_trade_signals(self) -> Dict[str, List[Dict[str, bool]]]:
        """
        Find trading signals across all viable pairs.

        Returns:
            Dictionary with market types as keys and lists of analysis results as values
        """
        signals = {
            "SPOT": [],
            "FUTURES": []
        }

        # Get viable trading pairs
        viable_pairs = self.exchange.get_viable_trading_pairs()

        # Analyze each pair for both market types
        for symbol in viable_pairs:
            for market_type in ["SPOT", "FUTURES"]:
                analysis_result = self.analyze_symbol(symbol, market_type)
                if analysis_result:
                    # Add symbol and market type to the result
                    analysis_result["symbol"] = symbol
                    analysis_result["market_type"] = market_type
                    signals[market_type].append(analysis_result)

        return signals
