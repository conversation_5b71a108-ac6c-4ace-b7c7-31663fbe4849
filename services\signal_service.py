"""
Signal service for the trading bot.
"""
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field, asdict

from models.database import get_active_signals, save_active_signal, update_active_signal as update_signal_db

logger = logging.getLogger(__name__)

@dataclass
class TradeSignal:
    """Class for representing a trade signal."""
    symbol: str
    direction: str  # "BUY" or "SELL"
    entry_price: float
    stop_loss: float
    targets: List[float]
    market_type: str = "SPOT"  # "SPOT" or "FUTURES"
    win_rate: float = 0.0
    timestamp: float = field(default_factory=lambda: datetime.now().timestamp())
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    status: str = "pending"  # pending, entry_triggered, target_hit, stop_loss_hit, closed
    targets_hit: List[int] = field(default_factory=list)  # Indices of targets that have been hit
    # Enhanced fields for detailed explanations
    technical_analysis: List[str] = field(default_factory=list)  # Detailed technical analysis points
    concepts_met: List[str] = field(default_factory=list)  # Trading concepts that were confirmed
    market_structure: str = ""  # Market structure analysis
    volume_analysis: str = ""  # Volume and institutional analysis
    risk_assessment: str = ""  # Risk assessment details
    quality_score: float = 0.0  # Quality score of the signal
    volatility: float = 0.0  # Asset volatility
    leverage_recommendation: str = ""  # Recommended leverage for futures
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the trade signal to a dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, signal_id: str, data: Dict[str, Any]) -> 'TradeSignal':
        """Create a trade signal from a dictionary."""
        return cls(
            id=signal_id,
            symbol=data.get("symbol", ""),
            direction=data.get("direction", ""),
            entry_price=data.get("entry_price", 0.0),
            stop_loss=data.get("stop_loss", 0.0),
            targets=data.get("targets", []),
            timestamp=data.get("timestamp", datetime.now().timestamp()),
            market_type=data.get("market_type", "SPOT"),
            win_rate=data.get("win_rate", 0.0),
            status=data.get("status", "pending"),
            targets_hit=data.get("targets_hit", []),
            technical_analysis=data.get("technical_analysis", []),
            concepts_met=data.get("concepts_met", []),
            market_structure=data.get("market_structure", ""),
            volume_analysis=data.get("volume_analysis", ""),
            risk_assessment=data.get("risk_assessment", ""),
            quality_score=data.get("quality_score", 0.0),
            volatility=data.get("volatility", 0.0),
            leverage_recommendation=data.get("leverage_recommendation", "")
        )
    
    def format_message(self) -> str:
        """Format the trade signal as a clean professional message without emojis."""
        # Format entry price
        entry_price_str = f"{self.entry_price:.8f}".rstrip('0').rstrip('.')

        # Format stop loss
        stop_loss_str = f"{self.stop_loss:.8f}".rstrip('0').rstrip('.')

        # Format targets - Pure SMC/ICT approach
        targets_str = []
        for i, target in enumerate(self.targets):
            target_str = f"{target:.8f}".rstrip('0').rstrip('.')
            targets_str.append(f"Target {i+1}: {target_str}")

        # Format clean professional message
        message = f"""<b>PREMIUM {self.market_type} SIGNAL</b>

<b>TRADE OVERVIEW</b>
<b>Pair:</b> {self.symbol}
<b>Direction:</b> {self.direction}
<b>Market Type:</b> {self.market_type}
<b>Quality Score:</b> {self.quality_score:.2f}/1.0

<b>TRADE PARAMETERS</b>
<b>Entry Price:</b> {entry_price_str}
<b>Stop Loss:</b> {stop_loss_str}

<b>PROFIT TARGETS</b>
"""

        for target in targets_str:
            message += f"{target}\n"

        # Add leverage recommendation for futures
        if self.market_type == "FUTURES" and self.leverage_recommendation:
            message += f"\n<b>Recommended Leverage:</b> {self.leverage_recommendation}\n"

        # Add SMC/ICT specific analysis section
        if self.technical_analysis:
            message += f"\n<b>SMC/ICT ANALYSIS</b>\n"
            for analysis in self.technical_analysis:
                # Make explanations more specific and professional
                if "fair value gap" in analysis.lower():
                    message += f"• <b>Fair Value Gap:</b> {analysis}\n"
                elif "order block" in analysis.lower():
                    message += f"• <b>Order Block:</b> {analysis}\n"
                elif "break of structure" in analysis.lower() or "bos" in analysis.lower():
                    message += f"• <b>Break of Structure:</b> {analysis}\n"
                elif "liquidity" in analysis.lower():
                    message += f"• <b>Liquidity Analysis:</b> {analysis}\n"
                elif "imbalance" in analysis.lower():
                    message += f"• <b>Market Imbalance:</b> {analysis}\n"
                elif "institutional" in analysis.lower() or "smart money" in analysis.lower():
                    message += f"• <b>Institutional Footprint:</b> {analysis}\n"
                elif "support" in analysis.lower() or "resistance" in analysis.lower():
                    message += f"• <b>Key Level:</b> {analysis}\n"
                elif "pattern" in analysis.lower():
                    message += f"• <b>Price Action Pattern:</b> {analysis}\n"
                else:
                    message += f"• {analysis}\n"

        # Add market structure analysis
        if self.market_structure:
            message += f"\n<b>MARKET STRUCTURE</b>\n"
            message += f"• {self.market_structure}\n"

        # Add concepts met section
        if self.concepts_met:
            message += f"\n<b>CONCEPTS CONFIRMED</b>\n"
            concept_explanations = {
                "ICT": "Inner Circle Trader concepts",
                "SMC": "Smart Money Concepts",
                "PA": "Price Action patterns",
                "S/R": "Support and Resistance levels",
                "Pattern Recognition": "Chart patterns"
            }

            for concept in self.concepts_met:
                explanation = concept_explanations.get(concept, concept)
                message += f"• <b>{concept}:</b> {explanation}\n"

        # Add status information
        if self.status == "entry_triggered":
            message += "\n\n<b>STATUS:</b> Entry triggered"
        elif self.status.startswith("target_hit"):
            message += "\n\n<b>STATUS:</b> Target(s) hit"
            for i in self.targets_hit:
                message += f"\nTarget {i+1} hit"
        elif self.status == "stop_loss_hit":
            message += "\n\n<b>STATUS:</b> Stop loss hit"
        elif self.status == "closed":
            message += "\n\n<b>STATUS:</b> Trade closed"

        return message
    
    def update_status(self, new_status: str, target_index: Optional[int] = None) -> None:
        """Update the status of the trade signal."""
        self.status = new_status
        
        if new_status == "target_hit" and target_index is not None:
            if target_index not in self.targets_hit:
                self.targets_hit.append(target_index)
    
    def get_alert_message(self) -> str:
        """Get an alert message based on the current status."""
        if self.status == "entry_triggered":
            return f"<b>ENTRY TRIGGERED</b> for {self.symbol} {self.direction} trade"
        elif self.status.startswith("target_hit"):
            target_index = self.targets_hit[-1] if self.targets_hit else 0
            return f"<b>TARGET {target_index+1} HIT</b> for {self.symbol} {self.direction} trade"
        elif self.status == "stop_loss_hit":
            return f"<b>STOP LOSS HIT</b> for {self.symbol} {self.direction} trade"
        elif self.status == "closed":
            return f"<b>TRADE CLOSED</b> for {self.symbol} {self.direction} trade"
        else:
            return f"Status update for {self.symbol} {self.direction} trade: {self.status}"

def get_active_signals() -> Dict[str, Dict[str, Any]]:
    """Get all active trade signals."""
    return get_active_signals()

def save_signal(signal: TradeSignal) -> bool:
    """Save a trade signal."""
    return save_active_signal(signal.id, signal.to_dict())

def update_signal(signal_id: str, signal_data: Dict[str, Any]) -> bool:
    """Update a trade signal."""
    return update_signal_db(signal_id, signal_data)

def get_signal(signal_id: str) -> Optional[TradeSignal]:
    """Get a trade signal by ID."""
    signals = get_active_signals()
    if signal_id in signals:
        return TradeSignal.from_dict(signal_id, signals[signal_id])
    return None
